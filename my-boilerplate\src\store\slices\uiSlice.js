import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  // Layout state
  sidebarOpen: true,
  sidebarCollapsed: false,
  topbarHeight: 64,
  
  // Theme state
  theme: 'light', // 'light', 'dark', 'system'
  
  // Modal state
  modals: {},
  
  // Loading overlays
  globalLoading: false,
  
  // Mobile state
  isMobile: false,
  
  // Navigation state
  activeMenuItem: null,
  breadcrumbs: [],
  
  // Search state
  searchOpen: false,
  searchQuery: '',
  
  // Notification panel
  notificationPanelOpen: false,
  
  // Settings panel
  settingsPanelOpen: false,
  
  // Layout configuration
  layoutConfig: {
    type: 'both', // 'sidebar', 'topbar', 'both'
    sidebarCollapsible: true,
    showBreadcrumbs: true,
    showNotifications: true,
    showSearch: true,
    showUserMenu: true,
  },
};

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    // Sidebar actions
    toggleSidebar: (state) => {
      state.sidebarOpen = !state.sidebarOpen;
    },
    setSidebarOpen: (state, action) => {
      state.sidebarOpen = action.payload;
    },
    toggleSidebarCollapsed: (state) => {
      state.sidebarCollapsed = !state.sidebarCollapsed;
    },
    setSidebarCollapsed: (state, action) => {
      state.sidebarCollapsed = action.payload;
    },
    
    // Theme actions
    setTheme: (state, action) => {
      state.theme = action.payload;
    },
    toggleTheme: (state) => {
      state.theme = state.theme === 'light' ? 'dark' : 'light';
    },
    
    // Modal actions
    openModal: (state, action) => {
      const { modalId, props = {} } = action.payload;
      state.modals[modalId] = { open: true, props };
    },
    closeModal: (state, action) => {
      const modalId = action.payload;
      if (state.modals[modalId]) {
        state.modals[modalId].open = false;
      }
    },
    closeAllModals: (state) => {
      Object.keys(state.modals).forEach(modalId => {
        state.modals[modalId].open = false;
      });
    },
    
    // Loading actions
    setGlobalLoading: (state, action) => {
      state.globalLoading = action.payload;
    },
    
    // Mobile actions
    setIsMobile: (state, action) => {
      state.isMobile = action.payload;
    },
    
    // Navigation actions
    setActiveMenuItem: (state, action) => {
      state.activeMenuItem = action.payload;
    },
    setBreadcrumbs: (state, action) => {
      state.breadcrumbs = action.payload;
    },
    addBreadcrumb: (state, action) => {
      state.breadcrumbs.push(action.payload);
    },
    removeBreadcrumb: (state, action) => {
      const index = action.payload;
      state.breadcrumbs.splice(index, 1);
    },
    
    // Search actions
    toggleSearch: (state) => {
      state.searchOpen = !state.searchOpen;
    },
    setSearchOpen: (state, action) => {
      state.searchOpen = action.payload;
    },
    setSearchQuery: (state, action) => {
      state.searchQuery = action.payload;
    },
    
    // Notification panel actions
    toggleNotificationPanel: (state) => {
      state.notificationPanelOpen = !state.notificationPanelOpen;
    },
    setNotificationPanelOpen: (state, action) => {
      state.notificationPanelOpen = action.payload;
    },
    
    // Settings panel actions
    toggleSettingsPanel: (state) => {
      state.settingsPanelOpen = !state.settingsPanelOpen;
    },
    setSettingsPanelOpen: (state, action) => {
      state.settingsPanelOpen = action.payload;
    },
    
    // Layout configuration actions
    updateLayoutConfig: (state, action) => {
      state.layoutConfig = { ...state.layoutConfig, ...action.payload };
    },
    resetLayoutConfig: (state) => {
      state.layoutConfig = initialState.layoutConfig;
    },
    
    // Responsive actions
    handleResize: (state, action) => {
      const { width } = action.payload;
      const isMobile = width < 768;
      state.isMobile = isMobile;
      
      // Auto-collapse sidebar on mobile
      if (isMobile && state.sidebarOpen) {
        state.sidebarOpen = false;
      }
    },
    
    // Reset UI state
    resetUI: (state) => {
      return { ...initialState, layoutConfig: state.layoutConfig };
    },
  },
});

export const {
  toggleSidebar,
  setSidebarOpen,
  toggleSidebarCollapsed,
  setSidebarCollapsed,
  setTheme,
  toggleTheme,
  openModal,
  closeModal,
  closeAllModals,
  setGlobalLoading,
  setIsMobile,
  setActiveMenuItem,
  setBreadcrumbs,
  addBreadcrumb,
  removeBreadcrumb,
  toggleSearch,
  setSearchOpen,
  setSearchQuery,
  toggleNotificationPanel,
  setNotificationPanelOpen,
  toggleSettingsPanel,
  setSettingsPanelOpen,
  updateLayoutConfig,
  resetLayoutConfig,
  handleResize,
  resetUI,
} = uiSlice.actions;

export default uiSlice.reducer;
