import { configureStore } from '@reduxjs/toolkit';
import authSlice from './slices/authSlice';
import uiSlice from './slices/uiSlice';
import websocketSlice from './slices/websocketSlice';
import employeeSlice from './slices/employeeSlice';

export const store = configureStore({
  reducer: {
    auth: authSlice,
    ui: uiSlice,
    websocket: websocketSlice,
    employees: employeeSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
        ignoredPaths: ['websocket.connection', 'websocket.socketIOConnection'],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export default store;
