import { io } from 'socket.io-client';
import { appConfig } from '@/config/app';
import {
  setWebSocketConnection,
  setWebSocketConnecting,
  setWebSocketError,
  incrementReconnectAttempts,
  resetReconnectAttempts,
  addMessage,
  updateHeartbeat,
  handleEmployeeUpdate,
  handleDashboardUpdate,
  handleNotificationReceived,
  handleUserStatusUpdate,
  handleBatchUpdate,
} from '@/store/slices/websocketSlice';
import {
  addRealTimeNotification,
  handleWebSocketNotification,
} from '@/store/slices/notificationSlice';

/**
 * WebSocket connection manager
 */
class WebSocketManager {
  constructor() {
    this.connection = null;
    this.store = null;
    this.reconnectTimer = null;
    this.heartbeatTimer = null;
    this.isReconnecting = false;
  }

  /**
   * Initialize WebSocket manager with Redux store
   * @param {Object} store - Redux store instance
   */
  init(store) {
    this.store = store;
  }

  /**
   * Connect to WebSocket server
   */
  connect() {
    if (!appConfig.websocket.enabled || this.connection) {
      return;
    }

    const state = this.store.getState();
    const { websocketUrl } = appConfig.websocket;
    const { maxReconnectAttempts, reconnectAttempts } = state.websocket;

    if (reconnectAttempts >= maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      return;
    }

    this.store.dispatch(setWebSocketConnecting(true));

    try {
      this.connection = new WebSocket(websocketUrl);

      this.connection.onopen = () => {
        console.log('WebSocket connected');
        this.store.dispatch(setWebSocketConnection(this.connection));
        this.store.dispatch(resetReconnectAttempts());
        this.startHeartbeat();
        this.isReconnecting = false;
      };

      this.connection.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          this.handleMessage(message);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      this.connection.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason);
        this.store.dispatch(setWebSocketConnection(null));
        this.stopHeartbeat();
        
        if (!this.isReconnecting && event.code !== 1000) {
          this.scheduleReconnect();
        }
      };

      this.connection.onerror = (error) => {
        console.error('WebSocket error:', error);
        this.store.dispatch(setWebSocketError('Connection failed'));
        this.store.dispatch(incrementReconnectAttempts());
      };

    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      this.store.dispatch(setWebSocketError(error.message));
      this.store.dispatch(incrementReconnectAttempts());
    }
  }

  /**
   * Disconnect from WebSocket server
   */
  disconnect() {
    this.isReconnecting = true;
    this.stopHeartbeat();
    this.clearReconnectTimer();

    if (this.connection) {
      this.connection.close(1000, 'Client disconnect');
      this.connection = null;
    }

    this.store.dispatch(setWebSocketConnection(null));
  }

  /**
   * Send message through WebSocket
   * @param {Object} message - Message to send
   */
  send(message) {
    if (this.connection && this.connection.readyState === WebSocket.OPEN) {
      const messageWithTimestamp = {
        ...message,
        timestamp: new Date().toISOString(),
      };
      this.connection.send(JSON.stringify(messageWithTimestamp));
      this.store.dispatch(addMessage({ ...messageWithTimestamp, direction: 'outgoing' }));
      return true;
    }
    return false;
  }

  /**
   * Handle incoming WebSocket messages
   * @param {Object} message - Received message
   */
  handleMessage(message) {
    this.store.dispatch(addMessage({ ...message, direction: 'incoming' }));

    switch (message.type) {
      case 'HEARTBEAT':
        this.store.dispatch(updateHeartbeat());
        break;

      case 'EMPLOYEE_UPDATE':
        this.store.dispatch(handleEmployeeUpdate(message.payload));
        break;

      case 'DASHBOARD_UPDATE':
        this.store.dispatch(handleDashboardUpdate(message.payload));
        break;

      case 'NOTIFICATION':
        this.store.dispatch(handleNotificationReceived(message.payload));
        this.store.dispatch(addRealTimeNotification(message.payload));
        break;

      case 'USER_STATUS_UPDATE':
        this.store.dispatch(handleUserStatusUpdate(message.payload));
        break;

      case 'BATCH_UPDATE':
        this.store.dispatch(handleBatchUpdate(message.payload));
        break;

      default:
        console.log('Unhandled WebSocket message type:', message.type);
    }
  }

  /**
   * Schedule reconnection attempt
   */
  scheduleReconnect() {
    const state = this.store.getState();
    const { reconnectInterval } = state.websocket;

    this.clearReconnectTimer();
    this.reconnectTimer = setTimeout(() => {
      console.log('Attempting to reconnect WebSocket...');
      this.connect();
    }, reconnectInterval);
  }

  /**
   * Clear reconnection timer
   */
  clearReconnectTimer() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  /**
   * Start heartbeat mechanism
   */
  startHeartbeat() {
    const state = this.store.getState();
    const { heartbeatInterval } = state.websocket.config;

    this.heartbeatTimer = setInterval(() => {
      this.send({ type: 'HEARTBEAT', payload: { timestamp: Date.now() } });
    }, heartbeatInterval);
  }

  /**
   * Stop heartbeat mechanism
   */
  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }
}

/**
 * Socket.IO connection manager
 */
class SocketIOManager {
  constructor() {
    this.socket = null;
    this.store = null;
  }

  /**
   * Initialize Socket.IO manager with Redux store
   * @param {Object} store - Redux store instance
   */
  init(store) {
    this.store = store;
  }

  /**
   * Connect to Socket.IO server
   */
  connect() {
    if (!appConfig.socketio.enabled || this.socket) {
      return;
    }

    const { socketIOUrl } = appConfig.socketio;
    const token = localStorage.getItem('token');

    this.socket = io(socketIOUrl, {
      auth: {
        token,
      },
      transports: ['websocket', 'polling'],
      timeout: 20000,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
    });

    this.setupEventListeners();
  }

  /**
   * Setup Socket.IO event listeners
   */
  setupEventListeners() {
    this.socket.on('connect', () => {
      console.log('Socket.IO connected');
      this.store.dispatch(setSocketIOConnection(this.socket));
    });

    this.socket.on('disconnect', (reason) => {
      console.log('Socket.IO disconnected:', reason);
      this.store.dispatch(setSocketIOConnection(null));
    });

    this.socket.on('connect_error', (error) => {
      console.error('Socket.IO connection error:', error);
      this.store.dispatch(setSocketIOError(error.message));
    });

    // Custom event listeners
    this.socket.on('employee:updated', (data) => {
      this.store.dispatch(handleEmployeeUpdate(data));
    });

    this.socket.on('dashboard:updated', (data) => {
      this.store.dispatch(handleDashboardUpdate(data));
    });

    this.socket.on('notification:new', (data) => {
      this.store.dispatch(handleNotificationReceived(data));
      this.store.dispatch(handleWebSocketNotification({
        type: 'NEW_NOTIFICATION',
        payload: data,
      }));
    });

    this.socket.on('user:status', (data) => {
      this.store.dispatch(handleUserStatusUpdate(data));
    });

    this.socket.on('batch:update', (data) => {
      this.store.dispatch(handleBatchUpdate(data));
    });
  }

  /**
   * Disconnect from Socket.IO server
   */
  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.store.dispatch(setSocketIOConnection(null));
  }

  /**
   * Emit event through Socket.IO
   * @param {string} event - Event name
   * @param {*} data - Data to send
   */
  emit(event, data) {
    if (this.socket && this.socket.connected) {
      this.socket.emit(event, data);
      return true;
    }
    return false;
  }

  /**
   * Join a room
   * @param {string} room - Room name
   */
  joinRoom(room) {
    this.emit('join:room', { room });
  }

  /**
   * Leave a room
   * @param {string} room - Room name
   */
  leaveRoom(room) {
    this.emit('leave:room', { room });
  }
}

// Create singleton instances
export const webSocketManager = new WebSocketManager();
export const socketIOManager = new SocketIOManager();

/**
 * Initialize both connection managers
 * @param {Object} store - Redux store instance
 */
export const initializeConnections = (store) => {
  webSocketManager.init(store);
  socketIOManager.init(store);

  // Auto-connect based on configuration
  if (appConfig.websocket.enabled) {
    webSocketManager.connect();
  }

  if (appConfig.socketio.enabled) {
    socketIOManager.connect();
  }
};

/**
 * Disconnect all connections
 */
export const disconnectAll = () => {
  webSocketManager.disconnect();
  socketIOManager.disconnect();
};
