import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

export interface Employee {
  id: string;
  name: string;
  email: string;
  position: string;
  department: string;
  phone?: string;
  avatar?: string;
  status: 'active' | 'inactive' | 'pending';
  joinDate: string;
  salary?: number;
  manager?: string;
  skills?: string[];
  location?: string;
}

interface EmployeeState {
  employees: Employee[];
  selectedEmployee: Employee | null;
  loading: {
    list: boolean;
    create: boolean;
    update: boolean;
    delete: boolean;
  };
  error: string | null;
  filters: {
    search: string;
    department: string;
    status: string;
  };
  pagination: {
    page: number;
    limit: number;
    total: number;
  };
}

const initialState: EmployeeState = {
  employees: [],
  selectedEmployee: null,
  loading: {
    list: false,
    create: false,
    update: false,
    delete: false,
  },
  error: null,
  filters: {
    search: '',
    department: '',
    status: '',
  },
  pagination: {
    page: 1,
    limit: 10,
    total: 0,
  },
};

// Async thunks
export const fetchEmployees = createAsyncThunk(
  'employees/fetchEmployees',
  async (params?: { page?: number; limit?: number; search?: string; department?: string; status?: string }) => {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.search) queryParams.append('search', params.search);
    if (params?.department) queryParams.append('department', params.department);
    if (params?.status) queryParams.append('status', params.status);

    const response = await fetch(`/api/employees?${queryParams}`);
    if (!response.ok) {
      throw new Error('Failed to fetch employees');
    }
    return response.json();
  }
);

export const createEmployee = createAsyncThunk(
  'employees/createEmployee',
  async (employeeData: Omit<Employee, 'id'>) => {
    const response = await fetch('/api/employees', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(employeeData),
    });
    if (!response.ok) {
      throw new Error('Failed to create employee');
    }
    return response.json();
  }
);

export const updateEmployee = createAsyncThunk(
  'employees/updateEmployee',
  async ({ id, data }: { id: string; data: Partial<Employee> }) => {
    const response = await fetch(`/api/employees/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error('Failed to update employee');
    }
    return response.json();
  }
);

export const deleteEmployee = createAsyncThunk(
  'employees/deleteEmployee',
  async (id: string) => {
    const response = await fetch(`/api/employees/${id}`, {
      method: 'DELETE',
    });
    if (!response.ok) {
      throw new Error('Failed to delete employee');
    }
    return { id };
  }
);

const employeeSlice = createSlice({
  name: 'employees',
  initialState,
  reducers: {
    setSelectedEmployee: (state, action: PayloadAction<Employee | null>) => {
      state.selectedEmployee = action.payload;
    },
    setFilters: (state, action: PayloadAction<Partial<EmployeeState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    setPagination: (state, action: PayloadAction<Partial<EmployeeState['pagination']>>) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
    clearError: (state) => {
      state.error = null;
    },
    resetFilters: (state) => {
      state.filters = initialState.filters;
    },
    // Real-time updates from WebSocket
    employeeUpdated: (state, action: PayloadAction<Employee>) => {
      const index = state.employees.findIndex(emp => emp.id === action.payload.id);
      if (index !== -1) {
        state.employees[index] = action.payload;
      }
    },
    employeeAdded: (state, action: PayloadAction<Employee>) => {
      state.employees.unshift(action.payload);
      state.pagination.total += 1;
    },
    employeeRemoved: (state, action: PayloadAction<string>) => {
      state.employees = state.employees.filter(emp => emp.id !== action.payload);
      state.pagination.total -= 1;
    },
  },
  extraReducers: (builder) => {
    // Fetch employees
    builder
      .addCase(fetchEmployees.pending, (state) => {
        state.loading.list = true;
        state.error = null;
      })
      .addCase(fetchEmployees.fulfilled, (state, action) => {
        state.loading.list = false;
        state.employees = action.payload.employees;
        state.pagination.total = action.payload.total;
      })
      .addCase(fetchEmployees.rejected, (state, action) => {
        state.loading.list = false;
        state.error = action.error.message || 'Failed to fetch employees';
      })
      // Create employee
      .addCase(createEmployee.pending, (state) => {
        state.loading.create = true;
        state.error = null;
      })
      .addCase(createEmployee.fulfilled, (state, action) => {
        state.loading.create = false;
        state.employees.unshift(action.payload);
        state.pagination.total += 1;
      })
      .addCase(createEmployee.rejected, (state, action) => {
        state.loading.create = false;
        state.error = action.error.message || 'Failed to create employee';
      })
      // Update employee
      .addCase(updateEmployee.pending, (state) => {
        state.loading.update = true;
        state.error = null;
      })
      .addCase(updateEmployee.fulfilled, (state, action) => {
        state.loading.update = false;
        const index = state.employees.findIndex(emp => emp.id === action.payload.id);
        if (index !== -1) {
          state.employees[index] = action.payload;
        }
        if (state.selectedEmployee?.id === action.payload.id) {
          state.selectedEmployee = action.payload;
        }
      })
      .addCase(updateEmployee.rejected, (state, action) => {
        state.loading.update = false;
        state.error = action.error.message || 'Failed to update employee';
      })
      // Delete employee
      .addCase(deleteEmployee.pending, (state) => {
        state.loading.delete = true;
        state.error = null;
      })
      .addCase(deleteEmployee.fulfilled, (state, action) => {
        state.loading.delete = false;
        state.employees = state.employees.filter(emp => emp.id !== action.payload.id);
        state.pagination.total -= 1;
        if (state.selectedEmployee?.id === action.payload.id) {
          state.selectedEmployee = null;
        }
      })
      .addCase(deleteEmployee.rejected, (state, action) => {
        state.loading.delete = false;
        state.error = action.error.message || 'Failed to delete employee';
      });
  },
});

export const {
  setSelectedEmployee,
  setFilters,
  setPagination,
  clearError,
  resetFilters,
  employeeUpdated,
  employeeAdded,
  employeeRemoved,
} = employeeSlice.actions;

export default employeeSlice.reducer;
