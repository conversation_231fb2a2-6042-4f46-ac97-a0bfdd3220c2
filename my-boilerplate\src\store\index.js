import { configureStore } from '@reduxjs/toolkit';
import authSlice from './slices/authSlice';
import uiSlice from './slices/uiSlice';
import employeeSlice from './slices/employeeSlice';
import loadingSlice from './slices/loadingSlice';
import notificationSlice from './slices/notificationSlice';
import websocketSlice from './slices/websocketSlice';

export const store = configureStore({
  reducer: {
    auth: authSlice,
    ui: uiSlice,
    employees: employeeSlice,
    loading: loadingSlice,
    notifications: notificationSlice,
    websocket: websocketSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['websocket/setConnection'],
        ignoredPaths: ['websocket.connection'],
      },
    }),
  devTools: process.env.NODE_ENV !== 'production',
});

export default store;
